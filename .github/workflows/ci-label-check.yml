name: Verify PR Label

on:
  merge_group:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
      - labeled
      - unlabeled

jobs:
  check-label:
    # Auto-succeed when triggered by merge_group event.
    if: ${{ github.event_name == 'pull_request' }}
    runs-on: ubuntu-latest
    steps:
      - name: Check PR label
        if: github.event.pull_request.user.login != 'dependabot'
        run: |
          LABEL_NAME="changelog:"
          if [[ $(curl -s "https://api.github.com/repos/${{ github.repository }}/pulls/${{ github.event.pull_request.number }}" | jq -r '.labels[].name' | grep -c "^$LABEL_NAME") -eq 0 ]]; then
            echo "Error: Pull request is missing a required label of the form '${LABEL_NAME}***'."
            exit 1
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
