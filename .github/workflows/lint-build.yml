name: "Lint and <PERSON><PERSON>"

on:
  merge_group:
  pull_request:
    branches: [main]
  push:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ (github.event.pull_request && github.event.pull_request.number) || github.ref || github.run_id }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  lint-build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@eef61447b9ff4aafe5dcd4e0bbf5d482be7e7871 # v4.2.1
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          cache: npm
          node-version: '22'
      - run: npm ci
      - name: Run depcheck
        run: npm run depcheck
      - run: npm run lint
      - run: npm run build
      - name: Ensure PR is not on main branch
        uses: jaegertracing/jaeger/.github/actions/block-pr-from-main-branch@main

  validate-renovate-config:
    runs-on: ubuntu-latest
    steps:
    - uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
      with:
        egress-policy: audit

    - uses: actions/checkout@d632683dd7b4114ad314bca15554477dd762a938 # v4.2.0
      with:
        submodules: false

    - name: validate renovate config
      run: |
        docker run \
          -v $PWD/renovate.json:/usr/src/app/renovate.json \
          ghcr.io/renovatebot/renovate:latest \
          renovate-config-validator
