{"private": true, "name": "jaeger-ui-monorepo", "version": "0.0.1", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/jaegertracing/jaeger-ui.git"}, "engines": {"node": ">=20"}, "devDependencies": {"@babel/eslint-parser": "^7.24.6", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "depcheck": "1.4.7", "eslint": "8.57.0", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "10.1.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "7.37.1", "husky": "9.1.0", "jsdom": "^26.0.0", "npm-run-all2": "8.0.1", "prettier": "3.5.0", "rxjs-compat": "6.6.7", "typescript": "5.8.2"}, "overrides": {"react": "18.3.1", "react-dom": "18.3.1", "cheerio": "1.0.0-rc.12"}, "workspaces": ["packages/plexus", "packages/jaeger-ui"], "scripts": {"build": "npm run --workspaces build", "check-license": "./scripts/check-license.sh", "coverage": "npm run --workspaces coverage", "depcheck": "./scripts/run-depcheck.sh", "eslint": "eslint --cache 'scripts/*.{js,jsx,ts,tsx}' 'packages/*/src/**/*.{js,jsx,ts,tsx}' 'packages/*/*.{js,jsx,ts,tsx,mts}'", "fmt": "npm run prettier", "lint": "npm-run-all -ln --parallel prettier-lint tsc-lint eslint check-license", "prepare": "npm run --workspace @jaegertracing/plexus prepublishOnly", "prettier": "prettier --write '{.,scripts}/*.{js,jsx,json,md,ts,tsx,mts}' 'packages/*/{src,demo/src}/**/!(layout.worker.bundled|react-vis).{css,js,jsx,json,md,ts,tsx}' 'packages/*/*.{css,js,jsx,json,md,ts,tsx,mts}'", "prettier-lint": "prettier --list-different '{.,scripts}/*.{js,jsx,json,md,ts,tsx,mts}' 'packages/*/{src,demo/src}/**/!(layout.worker.bundled|react-vis).{css,js,jsx,json,md,ts,tsx}' 'packages/*/*.{css,js,jsx,json,md,ts,tsx,mts}' || (echo '*** PLEASE RUN npm run prettier AND RESUBMIT ***' && false)", "test": "npm run --workspaces test --", "tsc-lint": "tsc --build", "tsc-lint-debug": "tsc --listFiles", "start": "cd packages/jaeger-ui && npm run start", "update-snapshots": "cd packages/jaeger-ui && npm test -- -u"}, "prettier": {"arrowParens": "avoid", "printWidth": 110, "proseWrap": "never", "singleQuote": true, "trailingComma": "es5"}, "husky": {"hooks": {"pre-commit": "npm-run-all -ln --parallel lint test"}}}