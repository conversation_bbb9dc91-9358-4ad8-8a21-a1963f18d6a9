# Build Considerations: Project Root

## `package.json`

### Dependencies (dev and otherwise)

#### `@typescript-eslint/eslint-plugin`

ESLint is being used to lint the repo, as a whole. Within `./packages/plexus` (for now), [`@typescript-eslint/eslint-plugin`](https://github.com/typescript-eslint/typescript-eslint/blob/master/packages/eslint-plugin) is used to apply ESLint to TypeScript. This application is localized to plexus via configuring `./packages/plexus/.eslintrc.js` for TypeScript, which means the change in settings is only applied to subdirectories of `./packages/plexus`. This package works really well, but there are quite a few issues it doesn't catch. For that, we use the TypeScript compiler.

#### `typescript`

In the project root, `typescript` is used to bolster the linting of TypeScript files. `tsc` catches quite a few issues that ESLint does not pick up on.

In `./packages/plexus`, `typescript` is used to generate type declarations for the ES module build. See [`./packages/plexus/BUILD.md`](packages/plexus/BUILD.md#typescript---emitdeclarationonly) for details.

### Scripts

#### `build`

`npm run build` executes the build in each of `./packages/*` sub-packages.

#### `eslint`

This applies ESLint to the repo, as a whole. The TypeScript linting has a distinct configuration, which is a descendent of `./.eslintrc.js`. See [TypeScript](#typescript), above.

#### `lint`

This is an amalgamation of linting scripts that run to make sure things are all-good. It's run in CI (travis) and as part of a pre-commit hook.

- `prettier-lint`
- `tsc-lint`
- `eslint`
- `check-license`

#### `prepare`

Runs after the top-level `npm install`. This ensures `./packages/plexus` builds and is available to `./packages/jaeger-ui`.

#### `prettier`, `prettier-lint`

`prettier` formats the code.

`prettier-lint` runs `bin-prettier` in the `--list-different` mode, which only outputs filenames if they would be changed by prettier formatting. If any such files are encountered, the program exits with a non-zero code. This is handy for blocking CI and pre-commits.

#### `tsc-lint`, `tsc-lint-debug`

`tsc` is run with the [`--noEmit`](https://www.typescriptlang.org/docs/handbook/compiler-options.html) option to bolster linting of TypeScript files. See [TypeScript](#typescript), above.

`tsc-lint-debug` is for diagnosing problems with linking, resolving files, or aliases in TypeScript code. It lists the files involved in the compilation.

### `test`

`test` runs tests for all packages.

Note that `./packages/plexus` does not yet have any tests, as tracked in issue [#340](https://github.com/jaegertracing/jaeger-ui/issues/340).

`./packages/jaeger-ui` uses [Jest](https://jestjs.io/) for testing. It can be useful to directly run tests for that package by running `npm test` from its directory, rather than the repository root. To run an individual test file specify the file name, e.g. `npm test src/utils/readJsonFile.test.js`.

#### Snapshots

Tests for React components in `./packages/jaeger-ui` make extensive use of Jest's [snapshot testing](https://jestjs.io/docs/29.6/snapshot-testing) functionality. These snapshots can be regenerated by running `npm run update-snapshots` to regenerate all snapshots, or `npm test -- -u -t <regex>` from the package directory to regenerate snapshots for a subset of tests only (`<regex>` matches against the full test name, [i.e. the test name and all surrounding describe blocks](https://jestjs.io/docs/29.6/cli#--testnamepatternregex)).

### `husky` . `hooks` . `pre-commit`

Runs the `lint` and `test` scripts.

## `.eslintrc.js`

Pretty basic.

Note: This configuration is extended by `./packages/plexus/.eslintrc.js`.

## `.github/workflows`

Holds GitHub Actions workflows used in CI and in release.

CodeCov is integrated into the unit tests workflow to report coverage data from `./packages/jaeger-ui`. When unit tests are added to Plexus, this integration will need to be updated to gather coverage data for Plexus as well.

[`npm ci`](https://docs.npmjs.com/cli/v10/commands/npm-ci) ensures installs in CI fail if they would typically mutate the lockfile.

## `tsconfig.json`

Used to configure the `tsc-lint` script and, in theory, the IDE (such as VS Code).

A few notable [compiler settings](http://www.typescriptlang.org/docs/handbook/compiler-options.html):

- `lib`
  - [es2017](https://github.com/Microsoft/TypeScript/blob/master/lib/lib.es2017.d.ts)
  - [dom](https://github.com/Microsoft/TypeScript/blob/master/lib/lib.dom.d.ts)
  - [dom.iterable](https://github.com/Microsoft/TypeScript/blob/master/lib/lib.dom.iterable.d.ts)
  - [webworker](https://github.com/Microsoft/TypeScript/blob/master/lib/lib.webworker.d.ts)
- `skipLibCheck` - Maybe worth reevaluating in the future
- `strict` - Important
- `noEmit` - We're using this for linting, after all
- `include` - We've included `./typgings` here because it turned out to be a lot simpler than configuring `types`, `typeRoots` and `paths`

## `typings/{custom.d.ts, index.d.ts}`

This is relevant for `./packages/plexus/src/LayoutManager/getLayout.tsx` and the `viz.js` package.

I wasn't able to get much in the line of error messaging, so I'm pretty vague on this.

The version of `viz.js` in use (1.8.1) ships with an `index.d.ts` file, but it has some issues. `./typings/custom.d.ts` defines an alternate type declaration for `viz.js` by targeting `viz.js/viz.js`. It was necessary use `./typings/index.d.ts` to refer to `./typings/custom.d.ts`. Then, importing the modules main file, which is `viz.js/viz.js`, will use the alternate type declaration.
