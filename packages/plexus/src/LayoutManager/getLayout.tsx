// Copyright (c) 2017 Uber Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { instance } from '@viz-js/viz';

import convPlain from './dot/convPlain';
import toDot from './dot/toDot';

import { EWorkerPhase, TLayoutOptions } from './types';
import { TEdge, TLayoutVertex, TSizeVertex } from '../types';

enum EValidity {
  Ok = 'Ok',
  Warn = 'Warn',
  Error = 'Error',
}

type TValidityError = {
  validity: EValidity.Error;
  message: string;
};

type TValidityOk = {
  validity: EValidity.Ok;
  message: null;
};

type TValidityWarn = {
  validity: EValidity.Warn;
  message: string;
};

type TVerticesValidity = TValidityError | TValidityOk | TValidityWarn;

const SHIFT_THRESHOLD = 0.015;

function isCloseEnough(a: number, b: number) {
  return Math.abs(a - b) / b < SHIFT_THRESHOLD;
}

function getVerticesValidity(
  input: TSizeVertex[] | TLayoutVertex[],
  output: TLayoutVertex[]
): TVerticesValidity {
  const inputHash: { [key: string]: TSizeVertex | TLayoutVertex } = {};
  input.forEach(v => {
    inputHash[String(v.vertex.key)] = v;
  });

  let warn: TVerticesValidity | void;

  for (let i = 0; i < output.length; i++) {
    const {
      vertex: { key },
      height,
      left,
      top,
      width,
    } = output[i];
    const src = inputHash[String(key)];
    if (!src) {
      return { validity: EValidity.Error, message: `Extra vertex found: ${key}` };
    }
    if (!isCloseEnough(src.height, height) || !isCloseEnough(src.width, width)) {
      return {
        validity: EValidity.Error,
        message: `Vertex ${key} failed size threshhold check (${SHIFT_THRESHOLD})`,
      };
    }
    if ('left' in src && 'top' in src) {
      const { left: srcLeft, top: srcTop } = src;
      if (!isCloseEnough(srcLeft, left) || !isCloseEnough(srcTop, top)) {
        warn = {
          validity: EValidity.Warn,
          message: `Vertex ${key} failed position threshhold check (${SHIFT_THRESHOLD})`,
        };
      }
    }
    delete inputHash[String(key)];
  }
  const missingKeys = Object.keys(inputHash);
  if (missingKeys.length !== 0) {
    const word = missingKeys.length > 1 ? 'vertices' : 'vertex';
    return { validity: EValidity.Error, message: `Missing ${word}: ${missingKeys.join(', ')}` };
  }
  warn ??= { validity: EValidity.Ok, message: null };
  return warn;
}

export default async function getLayout(
  phase: EWorkerPhase,
  inEdges: TEdge[],
  inVertices: TSizeVertex[] | TLayoutVertex[],
  layoutOptions: TLayoutOptions | null
) {
  const dot = toDot(inEdges, inVertices, layoutOptions);
  const { totalMemory = undefined, engine } = layoutOptions || {};
  const defaultEngine = phase === EWorkerPhase.Edges ? 'neato' : 'dot';
  const selectedEngine = engine || defaultEngine;

  const options = { totalMemory, engine: selectedEngine, format: 'plain' };

  const viz = await instance();
  const plainOut = viz.renderString(dot, options);

  const { edges, graph, vertices } = convPlain(plainOut, phase !== EWorkerPhase.Positions);
  const result = getVerticesValidity(inVertices, vertices);
  if (result.validity === EValidity.Error) {
    const message = result.message;
    return {
      graph,
      edges,
      vertices,
      layoutError: true,
      layoutErrorMessage: message,
    };
  }
  if (result.validity === EValidity.Warn) {
    return {
      graph,
      edges,
      vertices,
      layoutErrorMessage: result.message,
    };
  }
  return { edges, graph, vertices };
}
