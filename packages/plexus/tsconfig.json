// Copyright (c) 2019 Uber Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "compilerOptions": {
    "target": "es2016",
    "lib": ["es2017", "dom", "dom.iterable", "webworker"],
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "declaration": true,
    "emitDeclarationOnly": true,
    "jsx": "preserve",
    "outDir": "lib",
    "rootDir": "src",
    "composite": true
  },
  "include": [
    "src",
    // easier to add `./typings` here than deal with typeRoots, paths, etc.
    "./typings"
  ]
}
