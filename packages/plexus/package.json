{"name": "@jaegertracing/plexus", "license": "Apache-2.0", "version": "0.2.0", "description": "Directed Graph React component", "main": "lib/index.js", "files": ["lib", "dist"], "repository": {"type": "git", "url": "https://github.com/jaegertracing/jaeger-ui.git", "directory": "packages/plexus"}, "devDependencies": {"@babel/cli": "7.27.0", "@babel/core": "^7.25.2", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/preset-env": "^7.25.4", "@babel/preset-react": "7.27.1", "@babel/preset-typescript": "7.27.0", "@types/d3-zoom": "3.0.8", "@types/react": "^18.3.11", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "babel-loader": "10.0.0", "babel-plugin-transform-react-remove-prop-types": "0.4.24", "clean-webpack-plugin": "4.0.0", "css-loader": "7.1.0", "eslint-config-prettier": "^10.0.0", "html-loader": "5.1.0", "html-webpack-plugin": "5.6.0", "npm-run-all2": "8.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "rimraf": "6.0.1", "style-loader": "4.0.0", "webpack": "^5.92.0", "webpack-cli": "^6.0.0", "webpack-dev-server": "^5.0.4", "webpack-node-externals": "3.0.0", "worker-loader": "3.0.8"}, "peerDependencies": {"react": "^18.x", "react-dom": "^18.x"}, "dependencies": {"@viz-js/viz": "^3.11.0", "d3-selection": "^3.0.0", "d3-zoom": "^3.0.0", "memoize-one": "6.0.0", "react-icons": "^5.0.1"}, "scripts": {"_tasks/build/lib/js": "babel src --extensions '.tsx,.js' --out-dir lib", "_tasks/build/lib/types": "../../node_modules/.bin/tsc --build --force", "_tasks/build/umd": "webpack --mode $NODE_ENV --config webpack.umd.config.js", "_tasks/clean/dirs": "<PERSON><PERSON><PERSON> lib dist", "_tasks/clean/worker": "rimraf src/LayoutManager/layout.worker*js*", "_tasks/bundle-worker": "webpack --mode $NODE_ENV --config webpack.layout-worker.config.js", "_tasks/dev-server": "webpack-dev-server --mode $NODE_ENV --config webpack.dev.config.js", "build": "NODE_ENV=production npm-run-all -ln --serial _tasks/clean/* _tasks/bundle-worker --parallel _tasks/build/**", "coverage": "echo 'NO TESTS YET'", "prepublishOnly": "npm run build", "start": "NODE_ENV='development' npm-run-all -ln --serial _tasks/clean/worker _tasks/bundle-worker --parallel '_tasks/bundle-worker --watch' _tasks/dev-server", "test": "echo 'NO TESTS YET'"}}