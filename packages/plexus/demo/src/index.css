/*
Copyright (c) 2019 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

html {
  font-family: Arial, Helvetica, sans-serif;
}

.demo-row {
  display: flex;
  flex-wrap: nowrap;
}

.demo-row > :first-child {
  margin-right: 3rem;
}

.DemoGraph {
  border: 1px solid #bbb;
  cursor: move;
  overflow: hidden;
  height: 700px;
  width: 700px;
  position: relative;
}

.DemoGraph.is-small {
  height: 250px;
  width: 350px;
}

.DemoGraph--dag {
  background: #fafafa;
  stroke-width: 0.85;
}

.DemoGraph--node {
  background: #bbb;
  border: 1px solid #777;
  /* box-shadow: 0 0 7px 1px rgba(0, 0, 0, 0.4); */
  cursor: pointer;
  padding: 0.8em 1.25em;
  white-space: nowrap;
}

.DemoGraph--node.is-vector-bordered {
  border: none;
}

.DemoGraph--node--emphasized {
  position: absolute;
  /* content: ""; */
  left: -20px;
  right: -20px;
  top: -20px;
  bottom: -20px;
  background-image: repeating-linear-gradient(-60deg, #fffb8f, #fff566 3px, #fffb8f 5px);
  border: 1px dashed rgba(173, 139, 0, 0.5);
}

.DemoGraph--node--vectorBorder {
  fill: none;
  stroke: #777;
  stroke-width: 2;
}

.DemoGraph--node--vectorEmphasized {
  fill: none;
  stroke: #fff566;
  stroke-width: 10;
}

.DemoGraph--node--vectorEmphasized-border {
  fill: none;
  stroke: rgba(173, 139, 0, 0.5);
  stroke-width: 12;
}

.DemoGraph--node:hover {
  background: #666;
  color: #fff;
  border-color: #333;
}

.DemoGraph--dag.is-small .DemoGraph--nodeLabel {
  opacity: 0;
}

/* DAG minimap */

.Demo--miniMap {
  align-items: flex-end;
  bottom: 1rem;
  display: flex;
  left: 1rem;
  position: absolute;
  z-index: 1;
}

.Demo--miniMap > .plexus-MiniMap--item {
  border: 1px solid #333;
  background: #555;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
  margin-right: 1rem;
  position: relative;
}

.Demo--miniMap > .plexus-MiniMap--map {
  /* dynamic width, height */
  box-sizing: content-box;
}

.Demo--miniMap .plexus-MiniMap--mapActive {
  /* dynamic: width, height, transform */
  background: #bbb;
  outline: 1px solid #333;
  position: absolute;
}

.Demo--miniMap > .plexus-MiniMap--button {
  color: #bbb;
  cursor: pointer;
  font-size: 1.6em;
  line-height: 0;
  padding: 0.1rem;
}

.Demo--miniMap > .plexus-MiniMap--button:hover {
  background: #444;
  color: #ddd;
}
