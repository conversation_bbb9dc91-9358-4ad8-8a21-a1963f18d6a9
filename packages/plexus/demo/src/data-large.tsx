// Copyright (c) 2019 Uber Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import * as React from 'react';

import { TVertex } from '../../src/types';

export type TLargeNode = TVertex<{ service: string; operation: string }>;

export default {
  edges: [
    {
      from: 'fervent::ferve/fervent',
      to: 'carson::carso/carson',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'volhard::volha/volhard',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'yalow::yalow/yalow',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'mystifying::mysti/mystifying',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'meninsky::menin/meninsky',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'admiring::admir/admiring',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'fervent::ferve/fervent',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'hoover::hoove/hoover',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'brahmagupta::brahm/brahmagupta',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'banach::banac/banach',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'chatterjee::chatt/chatterjee',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'unruffled::unruf/unruffled',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'shockley::shock/shockley',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'pasteur::paste/pasteur',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'upbeat::upbea/upbeat',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'darwin::darwi/darwin',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'priceless::price/priceless',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'sammet::samme/sammet',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'serene::seren/serene',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'vigorous::vigor/vigorous',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'gracious::graci/gracious',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'hardcore::hardc/hardcore',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'elastic::elast/elastic',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'dazzling::dazzl/dazzling',
    },
    {
      from: 'fervent::ferve/fervent',
      to: 'neumann::neuma/neumann',
    },
    {
      from: 'keller::kelle/keller',
      to: 'fervent::ferve/fervent',
    },
    {
      from: 'keller::kelle/keller',
      to: 'shockley::shock/shockley',
    },
    {
      from: 'carson::carso/carson',
      to: 'heuristic::heuri/heuristic',
    },
    {
      from: 'murdock::murdo/murdock',
      to: 'fervent::ferve/fervent',
    },
    {
      from: 'mystifying::mysti/mystifying',
      to: 'eloquent::eloqu/eloquent',
    },
    {
      from: 'mystifying::mysti/mystifying',
      to: 'peaceful::peace/peaceful',
    },
    {
      from: 'shockley::shock/shockley',
      to: 'fervent::ferve/fervent',
    },
    {
      from: 'shockley::shock/shockley',
      to: 'volhard::volha/volhard',
    },
    {
      from: 'shockley::shock/shockley',
      to: 'brahmagupta::brahm/brahmagupta',
    },
    {
      from: 'shockley::shock/shockley',
      to: 'mystifying::mysti/mystifying',
    },
    {
      from: 'shockley::shock/shockley',
      to: 'darwin::darwi/darwin',
    },
    {
      from: 'shockley::shock/shockley',
      to: 'upbeat::upbea/upbeat',
    },
    {
      from: 'brahmagupta::brahm/brahmagupta',
      to: 'infallible::infal/infallible',
    },
    {
      from: 'brahmagupta::brahm/brahmagupta',
      to: 'heuristic::heuri/heuristic',
    },
    {
      from: 'brahmagupta::brahm/brahmagupta',
      to: 'easley::easle/easley',
    },
    {
      from: 'infallible::infal/infallible',
      to: 'sharp::sharp/sharp',
    },
    {
      from: 'banach::banac/banach',
      to: 'lalande::lalan/lalande',
    },
    {
      from: 'banach::banac/banach',
      to: 'brave::brave/brave',
    },
    {
      from: 'banach::banac/banach',
      to: 'wright::wrigh/wright',
    },
    {
      from: 'banach::banac/banach',
      to: 'jones::jones/jones',
    },
    {
      from: 'banach::banac/banach',
      to: 'bose::bose/bose',
    },
    {
      from: 'banach::banac/banach',
      to: 'ecstatic::ecsta/ecstatic',
    },
    {
      from: 'banach::banac/banach',
      to: 'volhard::volha/volhard',
    },
    {
      from: 'banach::banac/banach',
      to: 'tereshkova::teres/tereshkova',
    },
    {
      from: 'banach::banac/banach',
      to: 'lichterman::licht/lichterman',
    },
    {
      from: 'chatterjee::chatt/chatterjee',
      to: 'heuristic::heuri/heuristic',
    },
    {
      from: 'sharp::sharp/sharp',
      to: 'sharp::sharp/sharp',
    },
    {
      from: 'lalande::lalan/lalande',
      to: 'visvesvaraya::visve/visvesvaraya',
    },
    {
      from: 'upbeat::upbea/upbeat',
      to: 'tereshkova::teres/tereshkova',
    },
    {
      from: 'upbeat::upbea/upbeat',
      to: 'volhard::volha/volhard',
    },
    {
      from: 'priceless::price/priceless',
      to: 'priceless::price/priceless',
    },
    {
      from: 'sammet::samme/sammet',
      to: 'mclean::mclea/mclean',
    },
    {
      from: 'mclean::mclea/mclean',
      to: 'stupefied::stupe/stupefied',
    },
    {
      from: 'wright::wrigh/wright',
      to: 'bhabha::bhabh/bhabha',
    },
    {
      from: 'jones::jones/jones',
      to: 'youthful::youth/youthful',
    },
    {
      from: 'jones::jones/jones',
      to: 'heuristic::heuri/heuristic',
    },
    {
      from: 'youthful::youth/youthful',
      to: 'lumiere::lumie/lumiere',
    },
    {
      from: 'lumiere::lumie/lumiere',
      to: 'lumiere::lumie/lumiere',
    },
    {
      from: 'ecstatic::ecsta/ecstatic',
      to: 'golick::golic/golick',
    },
    {
      from: 'ecstatic::ecsta/ecstatic',
      to: 'goldstine::golds/goldstine',
    },
    {
      from: 'ecstatic::ecsta/ecstatic',
      to: 'volhard::volha/volhard',
    },
    {
      from: 'golick::golic/golick',
      to: 'zhukovsky::zhuko/zhukovsky',
    },
    {
      from: 'zhukovsky::zhuko/zhukovsky',
      to: 'zhukovsky::zhuko/zhukovsky',
    },
    {
      from: 'goldstine::golds/goldstine',
      to: 'dubinsky::dubin/dubinsky',
    },
    {
      from: 'dubinsky::dubin/dubinsky',
      to: 'dubinsky::dubin/dubinsky',
    },
    {
      from: 'vigorous::vigor/vigorous',
      to: 'joliot::jolio/joliot',
    },
    {
      from: 'vigorous::vigor/vigorous',
      to: 'vigorous::vigor/vigorous',
    },
    {
      from: 'vigorous::vigor/vigorous',
      to: 'pensive::pensi/pensive',
    },
    {
      from: 'jolly::jolly/jolly',
      to: 'fervent::ferve/fervent',
    },
    {
      from: 'euclid::eucli/euclid',
      to: 'jolly::jolly/jolly',
    },
    {
      from: 'boring::borin/boring',
      to: 'fervent::ferve/fervent',
    },
    {
      from: 'fermi::fermi/fermi',
      to: 'fervent::ferve/fervent',
    },
  ],
  vertices: [
    {
      key: 'fervent::ferve/fervent',
      service: 'fervent',
      operation: 'ferve/fervent',
    },
    {
      key: 'carson::carso/carson',
      service: 'carson',
      operation: 'carso/carson',
    },
    {
      key: 'volhard::volha/volhard',
      service: 'volhard',
      operation: 'volha/volhard',
    },
    {
      key: 'yalow::yalow/yalow',
      service: 'yalow',
      operation: 'yalow/yalow',
    },
    {
      key: 'mystifying::mysti/mystifying',
      service: 'mystifying',
      operation: 'mysti/mystifying',
    },
    {
      key: 'meninsky::menin/meninsky',
      service: 'meninsky',
      operation: 'menin/meninsky',
    },
    {
      key: 'admiring::admir/admiring',
      service: 'admiring',
      operation: 'admir/admiring',
    },
    {
      key: 'hoover::hoove/hoover',
      service: 'hoover',
      operation: 'hoove/hoover',
    },
    {
      key: 'brahmagupta::brahm/brahmagupta',
      service: 'brahmagupta',
      operation: 'brahm/brahmagupta',
    },
    {
      key: 'banach::banac/banach',
      service: 'banach',
      operation: 'banac/banach',
    },
    {
      key: 'chatterjee::chatt/chatterjee',
      service: 'chatterjee',
      operation: 'chatt/chatterjee',
    },
    {
      key: 'unruffled::unruf/unruffled',
      service: 'unruffled',
      operation: 'unruf/unruffled',
    },
    {
      key: 'shockley::shock/shockley',
      service: 'shockley',
      operation: 'shock/shockley',
    },
    {
      key: 'pasteur::paste/pasteur',
      service: 'pasteur',
      operation: 'paste/pasteur',
    },
    {
      key: 'upbeat::upbea/upbeat',
      service: 'upbeat',
      operation: 'upbea/upbeat',
    },
    {
      key: 'darwin::darwi/darwin',
      service: 'darwin',
      operation: 'darwi/darwin',
    },
    {
      key: 'priceless::price/priceless',
      service: 'priceless',
      operation: 'price/priceless',
    },
    {
      key: 'sammet::samme/sammet',
      service: 'sammet',
      operation: 'samme/sammet',
    },
    {
      key: 'serene::seren/serene',
      service: 'serene',
      operation: 'seren/serene',
    },
    {
      key: 'vigorous::vigor/vigorous',
      service: 'vigorous',
      operation: 'vigor/vigorous',
    },
    {
      key: 'gracious::graci/gracious',
      service: 'gracious',
      operation: 'graci/gracious',
    },
    {
      key: 'hardcore::hardc/hardcore',
      service: 'hardcore',
      operation: 'hardc/hardcore',
    },
    {
      key: 'elastic::elast/elastic',
      service: 'elastic',
      operation: 'elast/elastic',
    },
    {
      key: 'dazzling::dazzl/dazzling',
      service: 'dazzling',
      operation: 'dazzl/dazzling',
    },
    {
      key: 'neumann::neuma/neumann',
      service: 'neumann',
      operation: 'neuma/neumann',
    },
    {
      key: 'keller::kelle/keller',
      service: 'keller',
      operation: 'kelle/keller',
    },
    {
      key: 'heuristic::heuri/heuristic',
      service: 'heuristic',
      operation: 'heuri/heuristic',
    },
    {
      key: 'murdock::murdo/murdock',
      service: 'murdock',
      operation: 'murdo/murdock',
    },
    {
      key: 'eloquent::eloqu/eloquent',
      service: 'eloquent',
      operation: 'eloqu/eloquent',
    },
    {
      key: 'peaceful::peace/peaceful',
      service: 'peaceful',
      operation: 'peace/peaceful',
    },
    {
      key: 'infallible::infal/infallible',
      service: 'infallible',
      operation: 'infal/infallible',
    },
    {
      key: 'easley::easle/easley',
      service: 'easley',
      operation: 'easle/easley',
    },
    {
      key: 'sharp::sharp/sharp',
      service: 'sharp',
      operation: 'sharp/sharp',
    },
    {
      key: 'lalande::lalan/lalande',
      service: 'lalande',
      operation: 'lalan/lalande',
    },
    {
      key: 'brave::brave/brave',
      service: 'brave',
      operation: 'brave/brave',
    },
    {
      key: 'wright::wrigh/wright',
      service: 'wright',
      operation: 'wrigh/wright',
    },
    {
      key: 'jones::jones/jones',
      service: 'jones',
      operation: 'jones/jones',
    },
    {
      key: 'bose::bose/bose',
      service: 'bose',
      operation: 'bose/bose',
    },
    {
      key: 'ecstatic::ecsta/ecstatic',
      service: 'ecstatic',
      operation: 'ecsta/ecstatic',
    },
    {
      key: 'tereshkova::teres/tereshkova',
      service: 'tereshkova',
      operation: 'teres/tereshkova',
    },
    {
      key: 'lichterman::licht/lichterman',
      service: 'lichterman',
      operation: 'licht/lichterman',
    },
    {
      key: 'visvesvaraya::visve/visvesvaraya',
      service: 'visvesvaraya',
      operation: 'visve/visvesvaraya',
    },
    {
      key: 'mclean::mclea/mclean',
      service: 'mclean',
      operation: 'mclea/mclean',
    },
    {
      key: 'stupefied::stupe/stupefied',
      service: 'stupefied',
      operation: 'stupe/stupefied',
    },
    {
      key: 'bhabha::bhabh/bhabha',
      service: 'bhabha',
      operation: 'bhabh/bhabha',
    },
    {
      key: 'youthful::youth/youthful',
      service: 'youthful',
      operation: 'youth/youthful',
    },
    {
      key: 'lumiere::lumie/lumiere',
      service: 'lumiere',
      operation: 'lumie/lumiere',
    },
    {
      key: 'golick::golic/golick',
      service: 'golick',
      operation: 'golic/golick',
    },
    {
      key: 'goldstine::golds/goldstine',
      service: 'goldstine',
      operation: 'golds/goldstine',
    },
    {
      key: 'zhukovsky::zhuko/zhukovsky',
      service: 'zhukovsky',
      operation: 'zhuko/zhukovsky',
    },
    {
      key: 'dubinsky::dubin/dubinsky',
      service: 'dubinsky',
      operation: 'dubin/dubinsky',
    },
    {
      key: 'joliot::jolio/joliot',
      service: 'joliot',
      operation: 'jolio/joliot',
    },
    {
      key: 'pensive::pensi/pensive',
      service: 'pensive',
      operation: 'pensi/pensive',
    },
    {
      key: 'jolly::jolly/jolly',
      service: 'jolly',
      operation: 'jolly/jolly',
    },
    {
      key: 'euclid::eucli/euclid',
      service: 'euclid',
      operation: 'eucli/euclid',
    },
    {
      key: 'boring::borin/boring',
      service: 'boring',
      operation: 'borin/boring',
    },
    {
      key: 'fermi::fermi/fermi',
      service: 'fermi',
      operation: 'fermi/fermi',
    },
  ],
};

export function getNodeLabel(vertex: TVertex<{ key: string }>) {
  const [svc, op] = vertex.key.split('::', 2);
  return (
    <span className="DemoGraph--nodeLabel">
      <strong>{svc}</strong>
      <br />
      {op}
    </span>
  );
}
